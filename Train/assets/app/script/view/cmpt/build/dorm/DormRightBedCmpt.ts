import { BUILD_MOUNT_POINT } from "../../../../common/constant/Enums";
import StateObj from "../../../../model/passenger/StateObj";
import DormRightBedObj, { DormRightBedObjState } from "../../../../model/train/dorm/DormRightBedObj";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DormRightBedCmpt extends BuildCmpt {

    public model: DormRightBedObj = null
    private preState: StateObj<DormRightBedObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        if (type == DormRightBedObjState.UP) {
            sk.playAnimation("aniUp", false, data.timeData.elapsed)
        } else if (type == DormRightBedObjState.DOWN) {
            sk.playAnimation("aniDown", false, data.timeData.elapsed)
        } else {
            sk.playAnimation("jingzhi")
        }
    }

    public playOnJumpEnter() {
        let state = this.model.state
        let sk = this.Child("body", sp.Skeleton)
        let anim = "aniSit"
        if (state) {
            let type = state.type
            if (type == DormRightBedObjState.DOWN) {
                anim = "aniSitDown"
            }
        }
        sk.playAnimation(anim)
    }

    public playOnSit() {
        let body2Node = this.Component(MountPointCmpt).getPoint(BUILD_MOUNT_POINT.BODY2)
        if (body2Node) {
            body2Node.active = false
        }
    }
}
