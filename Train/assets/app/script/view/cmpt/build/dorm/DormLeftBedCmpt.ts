import { BUILD_MOUNT_POINT } from "../../../../common/constant/Enums";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DormLeftBedCmpt extends BuildCmpt {

    public playOnJumpEnter() {
        let sk = this.Child("body", sp.Skeleton)
        sk.playAnimation("aniSit")
    }

    public playOnSit(pointName: string) {
        if (pointName != BUILD_MOUNT_POINT.SLEEP) return
        let body2Node = this.Component(MountPointCmpt).getPoint(BUILD_MOUNT_POINT.BODY2)
        if (body2Node) {
            body2Node.active = false
        }
    }
}
