import PlanetEmptyNode from "../PlanetEmptyNode";

// 入口图1下层
export default class PlanetSki extends PlanetEmptyNode {

    public reachOffset: cc.Vec2 = cc.v2(-1502, -500)


    public async die() {
        this.dead = false
        this.map.nextNode()
        return false
    }

}


// 入口图2上层
export class PlanetSki2 extends PlanetEmptyNode {
    public reachOffset: cc.Vec2 = cc.v2(-700, 200)
    public originOffset: cc.Vec2 = cc.v2(200, 200)
    public jumpOffset: cc.Vec2 = cc.v2(400, 250)

    public async die() {
        this.dead = false
        this.map.nextNode()
        return false
    }
}