
import { DormBuildType } from "../../../common/constant/Enums";
import CarriageModel from "../common/CarriageModel";
import DormRightBedObj from "./DormRightBedObj";

/**
 * 一号宿舍
 */
export default class DormModel extends CarriageModel {

    public init(data) {
        super.init(data)
        return this
    }

    public getLeftBed() {
        return this.getBuildByOrder(DormBuildType.LEFT_BED)
    }

    public getRightBed() {
        return this.getBuildByOrder(DormBuildType.RIGHT_BED) as DormRightBedObj
    }

    public getBookcase() {
        return this.getBuildByOrder(DormBuildType.BOOKCASE)
    }

    public getLeftChair() {
        return this.getBuildByOrder(DormBuildType.LEFT_CHAIR)
    }

    public getChairs() {
        return [DormBuildType.LEFT_CHAIR, DormBuildType.RIGHT_CHAIR].map((type) => {
            return this.getBuildByOrder(type)
        }).filter(build => !!build)
    }

    public getBeds() {
        return [this.getLeftBed(), this.getRightBed()]
    }

    public newBuildObj(type: DormBuildType) {
        switch (type) {
            case DormBuildType.RIGHT_BED: return new DormRightBedObj()
            default:
                return super.newBuildObj(type)
        }
    }
}