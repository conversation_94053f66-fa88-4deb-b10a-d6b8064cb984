
import { res<PERSON>el<PERSON> } from "../../../common/helper/ResHelper"
import ActionTree, { ActionNode } from "../../passenger/ActionTree"
import PassengerModel from "../../passenger/PassengerModel"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

export enum DormRightBedObjState {
    UP,
    DOWN,
}

// 右边床
export default class DormRightBedObj extends BuildObj {

    public state: StateObj<DormRightBedObjState> = null

    public toUp() {
        this.reset()
        return this.actionTree.start(this.up)
    }

    public toDown() {
        this.reset()
        return this.actionTree.start(this.down)
    }

    public async up(action) {
        action.onTerminate = ()=>{
            this.setState()
        }
        let time = this.getAnimTime("aniUp")
        let timeData = new TimeStateData().init(time)
        this.setState(DormRightBedObjState.UP, {timeData})
        await action.wait(timeData)
        action.ok()
    }

    public async down(action: ActionNode) {
        action.onTerminate = ()=>{
            this.setState()
        }
        let time = this.getAnimTime("aniDown")
        let timeData = new TimeStateData().init(time)
        this.setState(DormRightBedObjState.DOWN, {timeData})
        await action.wait(timeData)
        action.ok()
    }

    public isDown() {
        return this.state?.type == DormRightBedObjState.DOWN
    }

    private setState(type?, data?) {
        this.state = new StateObj<DormRightBedObjState>().init(type, data)
    }

    public reset() {
        this.actionTree.terminate()
    }

    update(dt) {
        this.actionTree && this.actionTree.update(dt)
    }
}