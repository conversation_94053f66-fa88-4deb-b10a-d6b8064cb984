import CurvePath from "../../common/curve/CurvePath"
import SimplePath from "../../common/curve/SimplePath"

export default class CurvePathMoveModel {
    private path: CurvePath | SimplePath = null

    private pause: boolean = true

    protected speed: number = 300

    private dis: number = 0

    private listenerMap: any = {}

    private convertFunc: Function = null

    public get len() {
        if (this.path) return this.path.getLen()
        return 0
    }

    public init(path: CurvePath | SimplePath, convertFunc?: Function) {
        if (!path || this.path == path) return
        this.path = path
        this.dis = 0
        this.convertFunc = convertFunc
        return this
    }

    public setConvertFunc(convertFunc: Function) {
        this.convertFunc = convertFunc
    }

    public setEventListener(eventName: string, cb: Function, target) {
        this.listenerMap[eventName] = {cb, target}
    }

    public updateMovePosition(dt) {
        if (!this.isMoving()) return
        let preDis = this.dis
        this.dis += this.speed * dt
        if (this.dis > this.len) this.dis = this.len
        this.checkEvent(preDis, this.dis)
    }

    public getPosition(dis?: number, out?: cc.Vec2): cc.Vec2 {
        if (!this.path) return
        let ratio = 0
        if (this.len > 0) {
            ratio = (dis || this.dis) / this.len
        }
        let pos = this.path.getPosition(ratio, out)
        if (this.convertFunc) {
            return this.convertFunc(pos)
        }
        return pos
    }

    public isMoving() {
        if (this.pause || !this.path) return false
        return !this.isReach()
    }

    public setSpeed(speed) {
        this.speed = speed
    }

    public getSpeed() {
        return this.speed
    }

    public move() {
        this.pause = false
    }

    public stop() {
        this.pause = true
    }

    public isReach() {
        return this.dis >= this.len
    }

    public isPause() {
        return this.pause
    }

    public setDis(dis) {
        this.dis = dis
    }

    public getDir() {
        let curPos = this.getPosition(this.dis)
        let nextPos = this.getPosition(this.dis + 1)
        return nextPos.subSelf(curPos)
    }

    private checkEvent(preDis: number, dis: number) {
        if (!(this.path instanceof CurvePath)) return
        let preRatio = preDis / this.len
        let ratio = dis / this.len
        let event = this.path.getEventByRatio(preRatio, ratio)
        if (event) {
            let info = this.listenerMap[event.func]
            if (info) {
                info.cb.call(info.target, ...event.params)
            }
        }
    }

    public getRatio() {
        return this.dis / this.len
    }

    public setRatio(ratio) {
        this.dis = this.len * ratio
    }

    public getPath() {
        return this.path
    }

}